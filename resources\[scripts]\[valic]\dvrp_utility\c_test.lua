-- -- <PERSON><PERSON><PERSON><PERSON><PERSON> Crosshair Script pro FiveM
-- -- Zobrazuje klasickou bílou tečku při míření zbraní jako v base GTA V

-- -- Funkce pro vykreslení crosshairu (bílá tečka)
-- function DrawCrosshair()
--     local centerX = 0.5
--     local centerY = 0.5
--     local dotSize = 0.003 -- Velikost tečky
    
--     -- Vykreslení bílé tečky uprostřed obrazovky
--     DrawRect(centerX, centerY, dotSize, dotSize, 255, 255, 255, 255)
-- end

-- -- Hlavní thread pro crosshair
-- Citizen.CreateThread(function()
--     while true do
--         Citizen.Wait(0)
        
--         local playerPed = PlayerPedId()
        
--         -- <PERSON><PERSON><PERSON><PERSON> jest<PERSON> hráč míří
--         if IsPlayerFreeAiming(PlayerId()) then
--             -- S<PERSON><PERSON><PERSON><PERSON> původního crosshairu GTA V
--             HideHudComponentThisFrame(14) -- HUD_RETICLE
            
--             -- Vykreslen<PERSON> vlastn<PERSON>ho crosshairu (b<PERSON><PERSON><PERSON> teč<PERSON>)
--             DrawCrosshair()
--         end
--     end
-- end)

-- print("^2[Crosshai<PERSON> Script] ^7Načten - zobrazuje bílou tečku při míření.")

-- print("cernousek2")