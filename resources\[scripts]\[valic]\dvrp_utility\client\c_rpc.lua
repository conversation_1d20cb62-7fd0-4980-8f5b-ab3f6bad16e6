-- Client-side Discord RPC handling
local prevtime = GetGameTimer()
local prevframes = GetFrameCount()
local fps = -1

-- FPS calculation thread
CreateThread(function()
    while not NetworkIsPlayerActive(PlayerId()) or not NetworkIsSessionStarted() do         
        Wait(500)
        prevframes = GetFrameCount()
        prevtime = GetGameTimer()            
    end

    while true do         
        local curtime = GetGameTimer()
        local curframes = GetFrameCount()       
        
        if((curtime - prevtime) > 1000) then
            fps = (curframes - prevframes) - 1                
            prevtime = curtime
            prevframes = curframes
        end      
        Wait(350)
    end    
end)

-- Get active players count
function GetActivePlayers()
    local players = {}
    for i = 0, 128 do
        if NetworkIsPlayerActive(i) then
            table.insert(players, i)
        end
    end
    return #players
end

-- Set Discord Rich Presence
function SetRP()
    local name = GetPlayerName(PlayerId())
    local id = GetPlayerServerId(PlayerId())
    local playerCount = GetActivePlayers()

    -- Set Discord App ID and assets
    SetDiscordAppId(Config.DiscordAppId)
    SetDiscordRichPresenceAsset(Config.LargeLogo)
    SetDiscordRichPresenceAssetText(Config.LargeLogoText)
    SetDiscordRichPresenceAssetSmall(Config.SmallLogo)
    SetDiscordRichPresenceAssetSmallText(Config.SmallLogoText)
    
    -- Set rich presence text
    SetRichPresence(("FPS: %d | Nick: %s | ID: %d | Players: %d"):format(
        fps > 0 and fps or 0, 
        name, 
        id, 
        playerCount
    ))

    -- Set buttons
    if Config.Buttons and #Config.Buttons > 0 then
        for i = 1, math.min(2, #Config.Buttons) do
            local button = Config.Buttons[i]
            SetDiscordRichPresenceAction(i-1, button.label, button.url)
        end
    end
end

-- Main Discord RPC thread
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(1000) -- Update every second instead of every frame
        
        if Config.DiscordAppId and Config.DiscordAppId ~= '' then
            SetRP()
        end
    end
end)

-- Cleanup on resource stop
AddEventHandler('onResourceStop', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        SetDiscordRichPresenceAsset(nil)
        SetDiscordRichPresenceAssetText(nil)
        SetDiscordRichPresenceAssetSmall(nil)
        SetDiscordRichPresenceAssetSmallText(nil)
    end
end)