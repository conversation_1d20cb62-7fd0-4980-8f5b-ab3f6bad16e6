RegisterCommand('maxtunecar', function(source, args, rawCommand)
    local playerPed = GetPlayerPed(-1)
    if IsPedInAnyVehicle(playerPed, false) then
        local vehicle = GetVehiclePedIsIn(playerPed, false)
        
        -- Engine upgrade
        SetVehicleMod(vehicle, 11, GetNumVehicleMods(vehicle, 11) - 1, false)  -- Engine
        SetVehicleMod(vehicle, 12, GetNumVehicleMods(vehicle, 12) - 1, false)  -- Brakes
        SetVehicleMod(vehicle, 13, GetNumVehicleMods(vehicle, 13) - 1, false)  -- Transmission
        SetVehicleMod(vehicle, 15, GetNumVehicleMods(vehicle, 15) - 1, false)  -- Suspension
        SetVehicleMod(vehicle, 18, GetNumVehicleMods(vehicle, 18) - 1, false)  -- Turbo
        
        -- Toggle vehicle mods
        ToggleVehicleMod(vehicle, 18, true)  -- Turbo toggle
        
        -- Set vehicle to max tune
        SetVehicleFixed(vehicle)
        SetVehicleDirtLevel(vehicle, 0.0)
        
        TriggerEvent('chat:addMessage', {
            color = {255, 0, 0},
            multiline = true,
            args = {'[MaxTune]', 'Your vehicle has been fully upgraded!'}
        })
    else
        TriggerEvent('chat:addMessage', {
            color = {255, 0, 0},
            multiline = true,
            args = {'[MaxTune]', 'You must be in a vehicle to use this command!'}
        })
    end
end, false)